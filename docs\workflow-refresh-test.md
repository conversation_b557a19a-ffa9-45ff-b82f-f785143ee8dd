# 工作流页面刷新功能测试

## 问题背景

在工作流编排页面中，当用户刷新页面或直接通过URL访问时，`componentStore` 中的组件信息会丢失，导致页面无法正常显示工作流数据。

## 解决方案

通过 `componentStore.fetchComponentInfo()` 方法在页面初始化时总是重新获取组件信息，确保数据的完整性和准确性。

## 实现细节

### 1. 数据获取策略

```typescript
// 总是重新获取组件信息，确保数据是最新的
// 这样可以处理页面刷新、直接URL访问等情况
await componentStore.fetchComponentInfo(componentId.value);
```

### 2. 错误处理

- 组件不存在时显示错误消息并返回
- 功能不存在时显示错误消息并返回
- 网络错误时显示友好的错误提示

### 3. 路由参数监听

```typescript
// 监听路由参数变化，重新初始化数据
watch(
  [() => route.params.componentId, () => route.params.functionName],
  ([newComponentId, newFunctionName], [oldComponentId, oldFunctionName]) => {
    if (newComponentId !== oldComponentId || newFunctionName !== oldFunctionName) {
      initializeData();
    }
  }
);
```

## 测试场景

### 场景1: 页面刷新测试

1. **步骤**:
   - 正常进入工作流编排页面
   - 按 F5 刷新页面
   - 观察页面是否正常加载

2. **预期结果**:
   - 页面重新加载组件数据
   - 工作流编辑器正常显示
   - 功能数据完整加载

### 场景2: 直接URL访问测试

1. **步骤**:
   - 复制工作流编排页面的URL
   - 在新标签页中直接访问该URL
   - 观察页面加载情况

2. **预期结果**:
   - 页面从零开始加载组件数据
   - 正确显示指定的功能工作流
   - 所有功能正常可用

### 场景3: 无效参数测试

1. **步骤**:
   - 访问不存在的组件ID: `/workflow/invalid-id/testFunction`
   - 访问不存在的功能名: `/workflow/valid-id/invalidFunction`

2. **预期结果**:
   - 显示相应的错误消息
   - 自动返回到上一页面
   - 不会出现白屏或崩溃

### 场景4: 网络异常测试

1. **步骤**:
   - 断开网络连接
   - 刷新工作流编排页面
   - 观察错误处理

2. **预期结果**:
   - 显示网络错误提示
   - 提供重试或返回选项
   - 页面不会卡死

## 调试信息

页面加载过程中会输出以下调试信息：

```
正在加载组件信息: {componentId}
找到目标功能: {functionName}
工作流数据初始化完成
```

如果路由参数发生变化：

```
路由参数变化: {oldComponentId}/{oldFunctionName} -> {newComponentId}/{newFunctionName}
```

## 性能考虑

### 优化点

1. **缓存策略**: 如果组件数据已存在且ID匹配，可以考虑跳过重新获取
2. **加载状态**: 显示加载指示器，提升用户体验
3. **错误重试**: 提供重试机制，处理临时网络问题

### 当前实现

- 总是重新获取数据，确保数据准确性
- 显示加载状态 (`isDataLoaded`)
- 完善的错误处理和用户提示

## 测试检查清单

- [ ] 正常进入页面后刷新，数据正常加载
- [ ] 直接URL访问，页面正常显示
- [ ] 无效组件ID，显示错误并返回
- [ ] 无效功能名，显示错误并返回
- [ ] 网络异常时，显示友好错误提示
- [ ] 路由参数变化时，重新加载数据
- [ ] 控制台输出正确的调试信息
- [ ] 页面加载性能可接受（< 3秒）

## 注意事项

1. **数据一致性**: 总是获取最新数据，避免缓存问题
2. **用户体验**: 加载过程中显示适当的状态提示
3. **错误处理**: 提供清晰的错误信息和恢复路径
4. **性能平衡**: 在数据准确性和加载速度之间找到平衡

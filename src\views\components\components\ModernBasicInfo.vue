<template>
  <div class="business-basic-info">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">基本信息</h2>
      <div class="page-actions">
        <el-button
          v-if="!isEditing"
          type="primary"
          @click="handleEdit"
          class="edit-btn"
        >
          <el-icon><Edit /></el-icon>
          编辑信息
        </el-button>
        <template v-else>
          <el-button @click="cancelEdit" class="cancel-btn">取消</el-button>
          <el-button type="primary" @click="saveBasicInfo" class="save-btn">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
        </template>
      </div>
    </div>

    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 基础信息区域 -->
      <div class="form-section">
        <h3 class="section-title">基础信息</h3>
        <div class="form-grid">
          <div class="form-item">
            <label class="form-label">组件ID</label>
            <el-input v-model="formData.id" disabled class="form-input" />
          </div>
          <div class="form-item">
            <label class="form-label">组件名称</label>
            <el-input
              v-model="formData.name"
              :disabled="!isEditing"
              class="form-input"
              placeholder="请输入组件名称"
            />
          </div>
          <div class="form-item">
            <label class="form-label">版本号</label>
            <el-input
              v-model="formData.version"
              :disabled="!isEditing"
              class="form-input"
              placeholder="如: 1.0.0"
            />
          </div>
          <div class="form-item">
            <label class="form-label">作者</label>
            <el-input v-model="formData.author" disabled class="form-input" />
          </div>
        </div>
      </div>

      <!-- 描述信息区域 -->
      <div class="form-section">
        <h3 class="section-title">描述信息</h3>
        <div class="form-grid">
          <div class="form-item full-width">
            <label class="form-label">版本说明</label>
            <el-input
              v-model="formData.versionDescription"
              type="textarea"
              :rows="3"
              :disabled="!isEditing"
              class="form-textarea"
              placeholder="请输入版本更新说明..."
            />
          </div>
          <div class="form-item full-width">
            <label class="form-label">组件描述</label>
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="4"
              :disabled="!isEditing"
              class="form-textarea"
              placeholder="请输入组件的详细描述..."
            />
          </div>
        </div>
      </div>

      <!-- 标签和时间信息区域 -->
      <div class="form-section">
        <h3 class="section-title">标签与时间</h3>
        <div class="form-grid">
          <div class="form-item full-width">
            <label class="form-label">标签</label>
            <div class="tags-container">
              <div class="tags-list">
                <el-tag
                  v-for="tag in formData.tags"
                  :key="tag"
                  :closable="isEditing"
                  :disable-transitions="false"
                  class="tag-item"
                  @close="handleRemoveTag(tag)"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <div v-if="isEditing" class="tag-input-section">
                <el-input
                  v-if="inputTagVisible"
                  ref="tagInputRef"
                  v-model="inputTagValue"
                  class="tag-input"
                  size="small"
                  placeholder="输入标签名称"
                  @keyup.enter="handleInputConfirm"
                  @blur="handleInputConfirm"
                />
                <el-button
                  v-else
                  size="small"
                  @click="showTagInput"
                  class="add-tag-btn"
                >
                  <el-icon><Plus /></el-icon>
                  添加标签
                </el-button>
              </div>
            </div>
          </div>
          <div class="form-item">
            <label class="form-label">创建时间</label>
            <el-input
              v-model="formData.createTime"
              disabled
              class="form-input"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, watch, computed } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import {
  Edit,
  Check,
  InfoFilled,
  Document,
  PriceTag,
  Plus
} from "@element-plus/icons-vue";
import type { ComponentBasicInfoDto } from "@/types/componentType";
import { useComponentCategory } from "@/store/modules/componentCategory";

// Pinia store
const componentStore = useComponentCategory();

// 获取basicInfo
const storeBasicInfo = computed(
  () => componentStore.selectedComponent?.basicInfo || {}
);

// 是否处于编辑状态
const isEditing = ref(false);

// 表单数据
const formData = reactive<ComponentBasicInfoDto>({
  id: "",
  name: "",
  version: "",
  versionDescription: "",
  description: "",
  tags: [],
  createTime: "",
  author: ""
});

// 标签输入相关
const inputTagVisible = ref(false);
const inputTagValue = ref("");
const tagInputRef = ref();

// 监听store中的basicInfo变化
watch(
  storeBasicInfo,
  newBasicInfo => {
    if (newBasicInfo) {
      Object.assign(formData, {
        id: newBasicInfo.id || "",
        name: newBasicInfo.name || "",
        version: newBasicInfo.version || "",
        versionDescription: newBasicInfo.versionDescription || "",
        description: newBasicInfo.description || "",
        tags: newBasicInfo.tags || [],
        createTime: newBasicInfo.createTime || "",
        author: newBasicInfo.author || ""
      });
    }
  },
  { immediate: true, deep: true }
);

// 编辑处理
const handleEdit = () => {
  isEditing.value = true;
};

const cancelEdit = () => {
  isEditing.value = false;
  // 重置表单数据
  Object.assign(formData, storeBasicInfo.value);
};

const saveBasicInfo = async () => {
  try {
    await componentStore.updateBasicInfo(formData);
    isEditing.value = false;
    ElMessage.success("基本信息保存成功");
  } catch (error) {
    console.error("保存基本信息失败:", error);
    ElMessage.error("保存基本信息失败");
  }
};

// 标签处理
const handleRemoveTag = (tag: string) => {
  formData.tags = formData.tags.filter(t => t !== tag);
};

const showTagInput = () => {
  inputTagVisible.value = true;
  nextTick(() => {
    tagInputRef.value?.focus();
  });
};

const handleInputConfirm = () => {
  if (inputTagValue.value && !formData.tags.includes(inputTagValue.value)) {
    formData.tags.push(inputTagValue.value);
  }
  inputTagVisible.value = false;
  inputTagValue.value = "";
};
</script>

<style scoped lang="scss">
.modern-basic-info {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f2f5;

  .title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 12px;

    &::before {
      content: "";
      width: 4px;
      height: 24px;
      background: linear-gradient(
        135deg,
        var(--el-color-primary),
        var(--el-color-primary-light-3)
      );
      border-radius: 2px;
    }
  }

  .title-actions {
    display: flex;
    gap: 12px;

    .edit-btn,
    .save-btn {
      background: linear-gradient(
        135deg,
        var(--el-color-primary),
        var(--el-color-primary-light-3)
      );
      border: none;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
      }
    }

    .cancel-btn {
      border: 1px solid #dcdfe6;
      background: white;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  flex: 1;
}

.info-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f2f5;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  &.primary-card {
    grid-column: 1 / -1;
  }

  .card-header {
    padding: 24px 24px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    gap: 12px;

    .card-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(
        135deg,
        var(--el-color-primary),
        var(--el-color-primary-light-3)
      );
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
    }

    .card-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
    }
  }

  .card-content {
    padding: 24px;
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;

  &.full-width {
    grid-column: 1 / -1;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 4px;
  }

  .form-input,
  .form-textarea {
    :deep(.el-input__wrapper) {
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary-light-5);
      }

      &.is-focus {
        border-color: var(--el-color-primary);
        box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
      }
    }
  }
}

.tags-container {
  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;

    .tag-item {
      border-radius: 6px;
      font-size: 12px;
      padding: 4px 8px;
    }
  }

  .tag-input-section {
    display: flex;
    gap: 8px;
    align-items: center;

    .tag-input {
      width: 120px;
    }

    .add-tag-btn {
      border: 1px dashed #d1d5db;
      background: #f9fafb;
      color: #6b7280;
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
        background: var(--el-color-primary-light-9);
      }
    }
  }
}
</style>

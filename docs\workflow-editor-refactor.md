# 工作流编辑器重构说明

## 概述

本次重构将工作流编辑对话框从 `FunctionManager.vue` 中拆分出来，创建了一个独立的工作流编排页面。

## 主要变更

### 1. 新增工作流编排页面

**文件位置**: `src/views/workflow/index.vue`

**功能特性**:
- 独立的页面布局，包含页面头部和面包屑导航
- 返回按钮，可以返回到组件详情页面的功能管理tab
- 完整的工作流编辑器功能
- 页面标题显示当前编辑的功能名称
- 加载状态处理

### 2. 路由配置

**文件**: `src/router/modules/home.ts`

**新增路由**:
```typescript
{
  path: "/workflow/:componentId/:functionName",
  name: "WorkflowEditor", 
  component: () => import("@/views/workflow/index.vue"),
  meta: {
    title: "工作流编排",
    showLink: false
  }
}
```

**路由参数**:
- `componentId`: 组件ID
- `functionName`: 功能名称

### 3. FunctionManager.vue 修改

**移除的功能**:
- 工作流编辑对话框 (`workflowDialogVisible`)
- `WorkflowEditor` 组件导入
- `saveWorkflow` 函数
- 相关的样式定义

**修改的功能**:
- `handleEditWorkflow` 方法现在使用 `router.push` 跳转到新页面
- 添加了 `useRouter` 导入

### 4. 组件详情页面增强

**文件**: `src/views/components/detail.vue`

**新增功能**:
- 支持通过 query 参数 `tab` 设置活动标签页
- 从工作流页面返回时可以直接定位到功能管理tab

## 使用方式

### 1. 从功能管理页面进入工作流编排

1. 在组件详情页面，切换到"功能管理"tab
2. 点击任意功能行的"流程编排"按钮
3. 系统会跳转到独立的工作流编排页面

### 2. 工作流编排页面操作

- **编辑工作流**: 使用拖拽方式添加节点，配置节点参数
- **保存**: 点击页面头部的"保存"按钮
- **返回**: 点击页面头部的"返回"按钮，会回到组件详情页面的功能管理tab

### 3. URL 访问

可以直接通过 URL 访问工作流编排页面：
```
/workflow/{componentId}/{functionName}
```

例如：
```
/workflow/123/myFunction
```

## 技术实现细节

### 数据流

1. **页面初始化**:
   - 从路由参数获取 `componentId` 和 `functionName`
   - 检查 store 中是否已有对应组件数据
   - 如果没有，调用 `componentStore.fetchComponentInfo()` 加载组件数据
   - 查找指定的功能并设置为当前编辑功能

2. **数据保存**:
   - 使用现有的 `componentStore.saveCurrentFunction()` 方法
   - 保存成功后显示成功消息

3. **页面导航**:
   - 返回时跳转到 `/components/{componentId}?tab=functions`
   - 确保用户回到正确的tab页面

### 错误处理

- 如果组件不存在，显示错误消息并自动返回
- 如果功能不存在，显示错误消息并自动返回
- 加载过程中显示加载状态

## 优势

1. **更好的用户体验**: 独立页面提供更大的编辑空间
2. **URL 可分享**: 支持直接通过 URL 访问特定功能的工作流编排
3. **清晰的导航**: 面包屑导航和返回按钮提供清晰的页面层次
4. **代码解耦**: 工作流编辑逻辑与功能管理逻辑分离
5. **可扩展性**: 独立页面便于后续功能扩展

## 兼容性

- 保持了原有的 store 数据结构和方法
- `WorkflowEditor` 组件无需修改
- 现有的工作流数据格式保持不变

## 后续优化建议

1. 添加页面离开确认（如果有未保存的更改）
2. 支持键盘快捷键（如 Ctrl+S 保存）
3. 添加工作流预览功能
4. 支持工作流模板功能

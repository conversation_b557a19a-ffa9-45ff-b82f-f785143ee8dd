# 工作流编辑器功能测试

## 测试环境

- 开发服务器: http://localhost:8849/
- 浏览器: 任意现代浏览器

## 测试步骤

### 1. 基础功能测试

#### 1.1 访问组件列表
1. 打开 http://localhost:8849/
2. 应该看到零件列表页面
3. 点击任意组件进入详情页面

#### 1.2 进入功能管理
1. 在组件详情页面，点击"功能管理"tab
2. 应该看到功能列表
3. 每个功能行应该有"流程编排"按钮

#### 1.3 进入工作流编排页面
1. 点击任意功能的"流程编排"按钮
2. 应该跳转到新的工作流编排页面
3. URL 应该类似: `/workflow/{componentId}/{functionName}`

### 2. 工作流编排页面测试

#### 2.1 页面布局检查
- [ ] 页面头部包含返回按钮
- [ ] 面包屑导航显示: 零件管理 > 组件名称 > 工作流编排
- [ ] 右侧显示功能名称标签
- [ ] 工作流编辑器正常显示

#### 2.2 工作流编辑功能
- [ ] 左侧工具箱显示可用节点
- [ ] 可以拖拽节点到画布
- [ ] 可以连接节点
- [ ] 右侧配置面板正常工作
- [ ] 保存按钮可以点击

#### 2.3 导航功能
- [ ] 点击返回按钮能回到组件详情页面
- [ ] 返回后自动切换到"功能管理"tab
- [ ] 面包屑导航链接正常工作

### 3. 数据持久化测试

#### 3.1 保存工作流
1. 在工作流编排页面添加一些节点
2. 点击"保存"按钮
3. 应该显示"流程保存成功"消息

#### 3.2 数据保持
1. 保存后点击返回按钮
2. 再次进入同一功能的工作流编排
3. 之前添加的节点应该还在

### 4. 错误处理测试

#### 4.1 无效组件ID
1. 手动访问 `/workflow/invalid-id/testFunction`
2. 应该显示错误消息并自动返回

#### 4.2 无效功能名称
1. 手动访问 `/workflow/{validComponentId}/invalidFunction`
2. 应该显示"未找到功能"错误并自动返回

### 5. URL 直接访问测试

#### 5.1 直接访问工作流页面
1. 复制一个有效的工作流编排页面URL
2. 在新标签页中直接访问
3. 页面应该正常加载并显示对应的工作流

## 预期结果

### 成功标准
- [ ] 所有页面跳转正常
- [ ] 工作流编辑器功能完整
- [ ] 数据保存和加载正常
- [ ] 错误处理机制有效
- [ ] URL 直接访问支持

### 性能要求
- [ ] 页面加载时间 < 3秒
- [ ] 页面切换流畅无卡顿
- [ ] 工作流编辑器响应及时

## 已知问题

1. 如果后端API不可用，可能会有数据加载问题
2. 首次访问可能需要登录（如果有认证机制）

## 测试报告模板

```
测试日期: ____
测试人员: ____
浏览器: ____

基础功能测试: ✅/❌
工作流编排页面: ✅/❌  
导航功能: ✅/❌
数据持久化: ✅/❌
错误处理: ✅/❌
URL直接访问: ✅/❌

问题记录:
1. ____
2. ____

总体评价: ____
```

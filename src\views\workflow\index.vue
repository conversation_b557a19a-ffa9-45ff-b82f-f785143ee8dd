<template>
  <div class="workflow-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button type="text" class="back-button" @click="goBack">
          <el-icon class="mr-1"><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-divider direction="vertical" />
        <div class="breadcrumb">
          <span class="breadcrumb-item">零件管理</span>
          <el-icon class="breadcrumb-separator"><ArrowRight /></el-icon>
          <span class="breadcrumb-item">{{ componentName }}</span>
          <el-icon class="breadcrumb-separator"><ArrowRight /></el-icon>
          <span class="breadcrumb-item current">工作流编排</span>
        </div>
      </div>
      <div class="header-right">
        <el-tag type="info">{{ functionName }}</el-tag>
      </div>
    </div>

    <!-- 工作流编辑器 -->
    <div class="workflow-content">
      <WorkflowEditor v-if="isDataLoaded" @save="handleSave" @cancel="goBack" />
      <div v-else class="loading-container">
        <el-loading-directive v-loading="true" text="加载中..." />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import WorkflowEditor from "@/views/category/components/workflow/WorkflowEditor.vue";
import { useComponentCategory } from "@/store/modules/componentCategory";
import { cloneDeep } from "lodash-es";

defineOptions({
  name: "WorkflowPage"
});

const route = useRoute();
const router = useRouter();
const componentStore = useComponentCategory();

// 路由参数
const componentId = computed(() => route.params.componentId as string);
const functionName = computed(() => route.params.functionName as string);

// 页面状态
const isDataLoaded = ref(false);

// 计算属性
const componentName = computed(() => {
  return componentStore.selectedComponent?.basicInfo.name || "未知组件";
});

// 返回上一页
const goBack = () => {
  router.push({
    name: "ComponentDetail",
    params: { id: componentId.value },
    query: { tab: "functions" }
  });
};

// 处理保存
const handleSave = async (data: any) => {
  const result = await componentStore.saveCurrentFunction();
  if (result.success) {
    ElMessage.success("流程保存成功");
  } else {
    ElMessage.error(`流程保存失败: ${result.message}`);
  }
};

// 初始化数据
const initializeData = async () => {
  try {
    isDataLoaded.value = false;

    // 如果当前没有选中的组件，或者选中的组件ID不匹配，需要重新加载
    if (
      !componentStore.selectedComponent ||
      componentStore.selectedComponent.basicInfo?.id !== componentId.value
    ) {
      // 根据componentId加载组件数据
      await componentStore.fetchComponentInfo(componentId.value);
    }

    // 查找指定的功能
    const targetFunction = componentStore.selectedComponent?.functions?.find(
      func => func.name === functionName.value
    );

    if (!targetFunction) {
      ElMessage.error(`未找到功能: ${functionName.value}`);
      goBack();
      return;
    }

    // 设置当前编辑的功能
    const functionData = {
      ...targetFunction,
      flowData: cloneDeep(targetFunction.flowData) || "{}"
    };

    componentStore.setCurrentFunction(functionData);
    isDataLoaded.value = true;
  } catch (error) {
    console.error("初始化工作流数据失败:", error);
    ElMessage.error("加载工作流数据失败");
    goBack();
  }
};

onMounted(() => {
  initializeData();
});
</script>

<style scoped lang="scss">
.workflow-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  color: #606266;
  font-size: 14px;

  &:hover {
    color: #409eff;
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.breadcrumb-item {
  &.current {
    color: #303133;
    font-weight: 500;
  }
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #c0c4cc;
  font-size: 12px;
}

.header-right {
  display: flex;
  align-items: center;
}

.workflow-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.loading-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mr-1 {
  margin-right: 4px;
}
</style>

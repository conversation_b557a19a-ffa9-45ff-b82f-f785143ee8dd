<template>
  <div class="workflow-editor">
    <div class="editor-header">
      <div class="header-left">
        <el-input
          v-model="workflowName"
          placeholder="流程名称"
          class="name-input"
        />
        <el-input
          v-model="workflowDescription"
          placeholder="流程描述"
          class="description-input"
        />
      </div>
      <div class="header-right ml-4">
        <el-button type="primary" @click="saveWorkflow">
          <el-icon class="mr-1"><Check /></el-icon>保存
        </el-button>
      </div>
    </div>

    <div class="editor-content">
      <div class="toolbox-panel">
        <NodeToolbox />
      </div>

      <div class="canvas-panel">
        <VueFlow
          :default-zoom="1"
          :min-zoom="0.2"
          :max-zoom="4"
          :connection-mode="ConnectionMode.Strict"
          :connect-on-click="true"
          @dragover="onDragOver"
          @drop="onDrop"
          @node-click="onNodeClick"
          @connect="onConnect"
          @edges-change="onEdgesChange"
          @nodes-change="onNodesChange"
        >
          <template #node-custom="nodeProps">
            <Node
              :id="nodeProps.id"
              :node-data="nodeProps.data"
              :selected="nodeProps.selected"
            />
          </template>

          <Background :pattern-color="'#aaa'" :gap="8" />
          <Controls />
        </VueFlow>
      </div>

      <div class="config-panel">
        <NodeConfigPanel
          ref="configPanelRef"
          v-model="selectedNodeId"
          :removeNodes="removeNodes"
          @update-node-config="handleUpdateNodeConfig"
          @handle-delete-node="handleDeleteNode"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from "vue";
import { VueFlow, useVueFlow, ConnectionMode } from "@vue-flow/core";
import { ElMessage } from "element-plus";
import { Background } from "@vue-flow/background";
import { Controls } from "@vue-flow/controls";
import { Check } from "@element-plus/icons-vue";
import { generateId } from "@/utils/util";
import NodeToolbox from "./NodeToolbox.vue";
import NodeConfigPanel from "./NodeConfigPanel.vue";
import Node from "./Node.vue";
import { NodeType } from "@/types/componentType";
import { useComponentCategory } from "@/store/modules/componentCategory";

// 使用Pinia store
const componentStore = useComponentCategory();
const currentFunction = computed(() => componentStore.currentFunction);

const selectedNodeId = ref<string | null>(null);

// Vue Flow 实例
const flowInstance = useVueFlow();
const { addEdges, removeNodes, fromObject, getNodes, updateNode, findNode } =
  flowInstance;

// 工作流数据
const workflowName = computed({
  get: () => currentFunction.value?.name || "",
  set: val => {
    if (componentStore.currentFunction) {
      componentStore.currentFunction.name = val;
    }
  }
});

const workflowDescription = computed({
  get: () => currentFunction.value?.description || "",
  set: val => {
    if (componentStore.currentFunction) {
      componentStore.currentFunction.description = val;
    }
  }
});

// 配置面板引用
const configPanelRef = ref<InstanceType<typeof NodeConfigPanel> | null>(null);
const currentNodeId = ref("");

const handleUpdateNodeConfig = ({
  nodeId,
  config
}: {
  nodeId: string;
  config: Record<string, any>;
}) => {
  // 使用store更新节点配置
  componentStore.updateNodeConfig(nodeId, config);

  const node = findNode(nodeId);
  if (node) {
    // 更新节点的 data.configValue
    updateNode(nodeId, {
      data: {
        ...node.data,
        configValue: {
          ...node.data.configValue,
          [config.name]: config
        }
      }
    });
  } else {
    console.warn(`Node with id ${nodeId} not found for updating config.`);
  }
};

// 拖拽相关
const onDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = "move";
  }
};

const onDrop = (event: DragEvent) => {
  event.preventDefault();

  if (!event.dataTransfer) return;

  const data = event.dataTransfer.getData("application/vueflow");
  if (!data) return;

  const nodeData = JSON.parse(data);

  // 获取鼠标位置
  const { left, top } = (event.target as HTMLElement).getBoundingClientRect();
  const position = {
    x: event.clientX - left,
    y: event.clientY - top
  };

  // 开始节点的 outputParams 特殊处理
  if (nodeData.component === "Start") {
    nodeData.outputParams =
      currentFunction.value?.inputArgs?.map(arg => ({
        name: arg.name,
        valueType: arg.type
      })) || [];
  } else if (nodeData.component === "End") {
    nodeData.outputParams =
      currentFunction.value?.outputArgs?.map(arg => ({
        name: arg.name,
        valueType: arg.type
      })) || [];
  }

  // 创建新节点
  const newNode = {
    id: generateId(),
    type: "custom",
    position,
    data: nodeData
  };
  // addNodes([newNode]);
  componentStore.addFlowNode(newNode);

  // 选中新节点并确保容器尺寸
  nextTick(() => {
    onNodeClick({ node: { id: newNode.id } });
  });
};

// 节点点击
const onNodeClick = (event: { node: { id: string } }) => {
  const id: string = event.node.id;
  if (id && configPanelRef.value) {
    currentNodeId.value = id;
    configPanelRef.value.setSelectedNodeId(id);
  }
};

// 检查参数类型是否兼容
const isParamTypeCompatible = (
  sourceType: string,
  targetType: string
): boolean => {
  // 如果任一类型是'any'，则允许连接
  if (sourceType === "any" || targetType === "any") {
    return true;
  }

  // 否则只允许相同类型连接
  return sourceType === targetType;
};

// 连接节点
const onConnect = (params: any) => {
  // 确保连接参数有效
  if (params.source && params.target) {
    // 获取源节点和目标节点
    const sourceNode = getNodes.value.find(node => node.id === params.source);
    const targetNode = getNodes.value.find(node => node.id === params.target);

    // 检查节点类型限制
    if (sourceNode?.data.type === NodeType.END) {
      ElMessage.error("结束节点不能有出参连接");
      return;
    }

    if (targetNode?.data.type === NodeType.START) {
      ElMessage.error("开始节点不能有入参连接");
      return;
    }

    // 获取源节点和目标节点的参数类型
    const sourceEl = document.querySelector(
      `[data-handle-id="${params.sourceHandle}"]`
    );
    const targetEl = document.querySelector(
      `[data-handle-id="${params.targetHandle}"]`
    );

    const sourceType = sourceEl?.getAttribute("data-param-type") || "any";
    const targetType = targetEl?.getAttribute("data-param-type") || "any";

    // 检查参数类型是否兼容
    if (isParamTypeCompatible(sourceType, targetType)) {
      // 类型兼容，允许连接
      addEdges([
        {
          id: `${params.source}-${params.sourceHandle}-${params.target}-${params.targetHandle}`,
          source: params.source,
          target: params.target,
          sourceHandle: params.sourceHandle,
          targetHandle: params.targetHandle,
          data: {
            sourceType,
            targetType
          }
        }
      ]);
      // 更新节点关系
      componentStore.updateNodeRelation(params.source, params.target, "add");
      // getEdges
    } else {
      // 类型不兼容，显示错误提示
      ElMessage.error(`参数类型不兼容: ${sourceType} 无法连接到 ${targetType}`);
    }
  }
};

// 监听边的变化（包括断连事件）
const onEdgesChange = (changes: any[]) => {
  changes.forEach(change => {
    if (change.type === "remove") {
      // 处理边被删除/断连的逻辑
      console.log("边被断开连接:", change.id);
      handleEdgeDisconnect(change.id, change);
    }
  });
};

const handleDeleteNode = async () => {
  if (!selectedNodeId.value) return;

  try {
    removeNodes([selectedNodeId.value]);
    componentStore.flowData.nodes = componentStore.flowData.nodes.filter(
      (node: any) => node.id !== selectedNodeId.value
    );
    selectedNodeId.value = null;

    ElMessage.success({
      message: "节点已删除",
      duration: 1500
    });
  } catch (error) {
    console.log("用户取消删除节点");
  }
};

// 添加节点变更监听（只处理删除事件）
const onNodesChange = (changes: any[]) => {
  changes.forEach(change => {
    if (change.type === "remove") {
      handleDeleteNode();
    }
  });
};

// 处理边断连的逻辑
const handleEdgeDisconnect = (edgeId: string, changeData: any) => {
  const parseEdgeId = (edgeId: string) => {
    // 查找sourceHandle的位置（通常是"source"）
    const sourceHandleIndex = edgeId.indexOf("-source-");
    if (sourceHandleIndex === -1) {
      console.warn("无法解析边ID格式:", edgeId);
      return null;
    }

    const sourceNodeId = edgeId.substring(0, sourceHandleIndex);
    const remainingPart = edgeId.substring(sourceHandleIndex + 8);

    const targetHandleIndex = remainingPart.lastIndexOf("-target");
    if (targetHandleIndex === -1) {
      console.warn("无法解析边ID格式:", edgeId);
      return null;
    }

    const targetNodeId = remainingPart.substring(0, targetHandleIndex);
    const sourceHandle = "source";
    const targetHandle = remainingPart.substring(targetHandleIndex + 1); // +1 to skip '-'

    return {
      sourceNodeId,
      sourceHandle,
      targetNodeId,
      targetHandle
    };
  };

  const parsed = parseEdgeId(edgeId);
  if (parsed) {
    // 更新节点关系
    componentStore.updateNodeRelation(
      parsed.sourceNodeId,
      parsed.targetNodeId,
      "remove"
    );
  }
};

// 保存工作流
const saveWorkflow = async () => {
  const result = await componentStore.saveCurrentFunction();
  if (result.success) {
    ElMessage.success("流程保存成功");
  } else {
    ElMessage.error(`流程保存失败: ${result.message}`);
  }
};

onMounted(() => {
  componentStore.setFlowInstance(flowInstance);
  // 使用store中的流程数据
  if (componentStore.flowData) {
    fromObject(componentStore.flowData);
  }
});
</script>

<style>
@import url("@vue-flow/core/dist/style.css");
@import url("@vue-flow/core/dist/theme-default.css");
@import url("@vue-flow/controls/dist/style.css");

.vue-flow__container {
  width: 100% !important;
  height: 100% !important;
}
</style>

<style scoped>
.workflow-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 600px; /* 添加最小高度 */
  background-color: #f5f7fa;
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  flex: 1;
  gap: 16px;
}

.name-input {
  width: 200px;
}

.description-input {
  flex: 1;
}

.editor-content {
  display: flex;
  flex: 1;
  min-height: 500px; /* 添加最小高度 */
  overflow: hidden;
}

.toolbox-panel {
  width: 200px;
  overflow-y: auto;
  background-color: white;
}

.canvas-panel {
  position: relative;
  flex: 1;
  overflow: hidden;
  display: flex; /* 添加flex布局 */
  min-height: 500px; /* 增加最小高度 */
  width: 100%; /* 确保有宽度 */
}

.workflow-canvas {
  width: 100% !important; /* 强制设置宽度 */
  height: 100% !important; /* 强制设置高度 */
  flex: 1; /* 确保占满父容器 */
}

.config-panel {
  width: 300px;
  overflow-y: auto;
  background-color: white;
  border-left: 1px solid #e4e7ed;
}

.mr-1 {
  margin-right: 4px;
}
</style>

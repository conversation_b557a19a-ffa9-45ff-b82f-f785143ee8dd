<template>
  <div class="component-detail-page">
    <div class="page-header">
      <div class="breadcrumb-section">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ name: 'ComponentList' }">
            <el-icon><House /></el-icon>
            零件库
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{
            componentName || "加载中..."
          }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <div class="action-section">
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
        <el-button type="primary" @click="saveComponent">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <div v-if="componentStore.selectedComponentId" class="detail-container">
        <el-tabs v-model="activeTab" type="border-card" class="detail-tabs">
          <el-tab-pane label="基本信息" name="basic">
            <BasicInfo />
          </el-tab-pane>
          <el-tab-pane label="属性管理" name="attributes">
            <AttributeManager />
          </el-tab-pane>
          <el-tab-pane label="子零件管理" name="subcomponents">
            <SubcomponentManager />
          </el-tab-pane>
          <el-tab-pane label="功能管理" name="functions">
            <FunctionManager />
          </el-tab-pane>
          <el-tab-pane label="报警管理" name="alarms">
            <AlarmManager />
          </el-tab-pane>
        </el-tabs>
      </div>

      <div v-else class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { House, ArrowLeft, Check } from "@element-plus/icons-vue";

// 导入现有的管理组件
import BasicInfo from "@/views/category/components/BasicInfo.vue";
import AttributeManager from "@/views/category/components/AttributeManager.vue";
import SubcomponentManager from "@/views/category/components/SubcomponentManager.vue";
import FunctionManager from "@/views/category/components/FunctionManager.vue";
import AlarmManager from "@/views/category/components/AlarmManager.vue";

// 导入 store
import { useComponentCategory } from "@/store/modules/componentCategory";

defineOptions({
  name: "ComponentDetail"
});

const route = useRoute();
const router = useRouter();
const componentStore = useComponentCategory();

// 当前选中的标签页
const activeTab = ref("basic");

// 监听路由query参数，设置活动tab
watch(
  () => route.query.tab,
  newTab => {
    if (newTab && typeof newTab === "string") {
      activeTab.value = newTab;
    }
  },
  { immediate: true }
);

// 组件名称
const componentName = computed(
  () => componentStore.selectedComponent?.basicInfo?.name || ""
);

// 监听路由参数变化
watch(
  () => route.params.id,
  async newId => {
    if (newId) {
      try {
        await componentStore.fetchComponentInfo(newId as string);
      } catch (error) {
        console.error("获取组件详情失败:", error);
        ElMessage.error("获取组件详情失败");
        goBack();
      }
    }
  },
  { immediate: true }
);

// 返回列表
const goBack = () => {
  router.push({ name: "ComponentList" });
};

// 保存组件
const saveComponent = async () => {
  try {
    const result = await componentStore.updateCurrentComponent(
      componentStore.selectedComponent
    );

    if (result.success) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(result.message || "保存失败");
    }
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key === "s") {
    event.preventDefault();
    saveComponent();
  } else if (event.key === "Escape") {
    goBack();
  }
};

onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>

<style scoped lang="scss">
.component-detail-page {
  height: calc(100vh - 106px);
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;

  .breadcrumb-section {
    .el-breadcrumb {
      font-size: 14px;

      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          display: flex;
          align-items: center;
          gap: 4px;
          color: #606266;

          &:hover {
            color: var(--el-color-primary);
          }
        }

        &:last-child .el-breadcrumb__inner {
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }

  .action-section {
    display: flex;
    gap: 12px;
  }
}

.page-content {
  flex: 1;
  padding: 24px;
  overflow: auto;

  .detail-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .detail-tabs {
      :deep(.el-tabs__header) {
        margin: 0;
        background: #fafafa;
        border-radius: 8px 8px 0 0;
      }

      :deep(.el-tabs__content) {
        padding: 24px;
      }
    }
  }

  .loading-container {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}
</style>

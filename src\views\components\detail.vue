<template>
  <div class="component-detail-page">
    <!-- 顶部导航栏 -->
    <div class="page-header">
      <div class="header-left">
        <div class="breadcrumb-section">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ name: 'ComponentList' }">
              <el-icon><House /></el-icon>
              零件库
            </el-breadcrumb-item>
            <el-breadcrumb-item>{{
              componentName || "加载中..."
            }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 组件标题和状态 -->
        <div class="component-title-section">
          <div class="title-wrapper">
            <div class="component-icon">
              <el-icon><Box /></el-icon>
            </div>
            <div class="title-info">
              <h1 class="component-title">
                {{ componentName || "加载中..." }}
              </h1>
              <div class="component-meta">
                <el-tag size="small" type="success"
                  >v{{ componentVersion }}</el-tag
                >
                <span class="meta-divider">•</span>
                <span class="author">{{ componentAuthor }}</span>
                <span class="meta-divider">•</span>
                <span class="update-time">{{
                  formatDate(componentUpdateTime)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="action-section">
        <el-button class="back-btn" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
        <el-button type="primary" class="save-btn" @click="saveComponent">
          <el-icon><Check /></el-icon>
          保存更改
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div v-if="componentStore.selectedComponentId" class="detail-container">
        <!-- 侧边导航 -->
        <div class="sidebar-nav">
          <div class="nav-title">管理面板</div>
          <div class="nav-menu">
            <div
              v-for="tab in tabList"
              :key="tab.name"
              :class="['nav-item', { active: activeTab === tab.name }]"
              @click="activeTab = tab.name"
            >
              <el-icon class="nav-icon">
                <component :is="tab.icon" />
              </el-icon>
              <span class="nav-label">{{ tab.label }}</span>
              <div v-if="activeTab === tab.name" class="nav-indicator" />
            </div>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
          <div class="content-wrapper">
            <Transition name="fade-slide" mode="out-in">
              <div :key="activeTab" class="tab-content">
                <ModernBasicInfo v-if="activeTab === 'basic'" />
                <AttributeManager v-else-if="activeTab === 'attributes'" />
                <SubcomponentManager
                  v-else-if="activeTab === 'subcomponents'"
                />
                <FunctionManager v-else-if="activeTab === 'functions'" />
                <AlarmManager v-else-if="activeTab === 'alarms'" />
              </div>
            </Transition>
          </div>
        </div>
      </div>

      <div v-else class="loading-container">
        <div class="loading-content">
          <el-skeleton :rows="8" animated />
          <div class="loading-text">正在加载组件信息...</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import {
  House,
  ArrowLeft,
  Check,
  Box,
  User,
  Setting,
  Grid,
  Tools,
  Bell
} from "@element-plus/icons-vue";

// 导入现有的管理组件
import ModernBasicInfo from "@/views/components/components/ModernBasicInfo.vue";
import AttributeManager from "@/views/category/components/AttributeManager.vue";
import SubcomponentManager from "@/views/category/components/SubcomponentManager.vue";
import FunctionManager from "@/views/category/components/FunctionManager.vue";
import AlarmManager from "@/views/category/components/AlarmManager.vue";

// 导入 store
import { useComponentCategory } from "@/store/modules/componentCategory";

defineOptions({
  name: "ComponentDetail"
});

const route = useRoute();
const router = useRouter();
const componentStore = useComponentCategory();

// 当前选中的标签页
const activeTab = ref("basic");

// 监听路由query参数，设置活动tab
watch(
  () => route.query.tab,
  newTab => {
    if (newTab && typeof newTab === "string") {
      activeTab.value = newTab;
    }
  },
  { immediate: true }
);

// 组件信息计算属性
const componentName = computed(
  () => componentStore.selectedComponent?.basicInfo?.name || ""
);

const componentVersion = computed(
  () => componentStore.selectedComponent?.basicInfo?.version || "1.0.0"
);

const componentAuthor = computed(
  () => componentStore.selectedComponent?.basicInfo?.author || "未知"
);

const componentUpdateTime = computed(
  () => componentStore.selectedComponent?.basicInfo?.createTime || ""
);

// 标签页配置
const tabList = ref([
  {
    name: "basic",
    label: "基本信息",
    icon: "User"
  },
  {
    name: "attributes",
    label: "属性管理",
    icon: "Setting"
  },
  {
    name: "subcomponents",
    label: "子零件管理",
    icon: "Grid"
  },
  {
    name: "functions",
    label: "功能管理",
    icon: "Tools"
  },
  {
    name: "alarms",
    label: "报警管理",
    icon: "Bell"
  }
]);

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return "";
  const date = new Date(dateStr);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit"
  });
};

// 监听路由参数变化
watch(
  () => route.params.id,
  async newId => {
    if (newId) {
      try {
        await componentStore.fetchComponentInfo(newId as string);
      } catch (error) {
        console.error("获取组件详情失败:", error);
        ElMessage.error("获取组件详情失败");
        goBack();
      }
    }
  },
  { immediate: true }
);

// 返回列表
const goBack = () => {
  router.push({ name: "ComponentList" });
};

// 保存组件
const saveComponent = async () => {
  try {
    const result = await componentStore.updateCurrentComponent(
      componentStore.selectedComponent
    );

    if (result.success) {
      ElMessage.success("保存成功");
    } else {
      ElMessage.error(result.message || "保存失败");
    }
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey && event.key === "s") {
    event.preventDefault();
    saveComponent();
  } else if (event.key === "Escape") {
    goBack();
  }
};

onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>

<style scoped lang="scss">
.component-detail-page {
  height: calc(100vh - 106px);
  display: flex;
  flex-direction: column;
  background: #fafbfc;
  min-height: 100vh;
}

.page-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .header-left {
    flex: 1;

    .breadcrumb-section {
      margin-bottom: 16px;

      .el-breadcrumb {
        font-size: 14px;

        :deep(.el-breadcrumb__item) {
          .el-breadcrumb__inner {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #606266;
            transition: color 0.3s ease;

            &:hover {
              color: var(--el-color-primary);
            }
          }

          &:last-child .el-breadcrumb__inner {
            color: #303133;
            font-weight: 500;
          }
        }
      }
    }

    .component-title-section {
      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 16px;

        .component-icon {
          width: 48px;
          height: 48px;
          background: #f8fafc;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #64748b;
          font-size: 20px;
        }

        .title-info {
          .component-title {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.3;
            margin-bottom: 6px;
          }

          .component-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 13px;
            color: #6b7280;

            .meta-divider {
              color: #d1d5db;
              font-weight: normal;
            }

            .author {
              font-weight: 500;
              color: #374151;
            }

            .update-time {
              color: #9ca3af;
            }
          }
        }
      }
    }
  }

  .action-section {
    display: flex;
    gap: 12px;
    align-items: flex-start;
    margin-top: 20px;

    .back-btn {
      border: 1px solid #d1d5db;
      background: white;
      color: #374151;
      transition: all 0.2s ease;

      &:hover {
        border-color: #9ca3af;
        background: #f9fafb;
      }
    }

    .save-btn {
      background: #3b82f6;
      border: 1px solid #3b82f6;
      color: white;
      transition: all 0.2s ease;

      &:hover {
        background: #2563eb;
        border-color: #2563eb;
      }
    }
  }
}

.page-content {
  flex: 1;
  padding: 0;
  overflow: hidden;

  .detail-container {
    height: 100%;
    display: flex;
    background: #ffffff;
    overflow: hidden;
  }

  .loading-container {
    background: #ffffff;
    padding: 48px;
    text-align: center;

    .loading-content {
      .loading-text {
        margin-top: 24px;
        color: #6b7280;
        font-size: 16px;
      }
    }
  }
}

// 侧边导航样式
.sidebar-nav {
  width: 240px;
  background: #f8fafc;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;

  .nav-title {
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
    padding: 24px 20px 16px;
    margin-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
  }

  .nav-menu {
    flex: 1;
    padding: 8px 0;

    .nav-item {
      position: relative;
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 20px;
      color: #6b7280;
      cursor: pointer;
      transition: all 0.2s ease;
      margin: 0 8px;
      border-radius: 6px;

      &:hover {
        background: #f1f5f9;
        color: #374151;
      }

      &.active {
        background: #eff6ff;
        color: #2563eb;

        .nav-indicator {
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 20px;
          background: #2563eb;
          border-radius: 0 2px 2px 0;
        }
      }

      .nav-icon {
        font-size: 18px;
        flex-shrink: 0;
      }

      .nav-label {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

// 内容区域样式
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .content-wrapper {
    flex: 1;
    padding: 32px 40px;
    overflow: auto;
    background: #ffffff;

    .tab-content {
      height: 100%;
      max-width: 1200px;
    }
  }
}

// 过渡动画
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
